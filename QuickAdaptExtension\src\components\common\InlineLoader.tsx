import React from 'react';
import './InlineLoader.css';

interface InlineLoaderProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
  message?: string;
}

const InlineLoader: React.FC<InlineLoaderProps> = ({ 
  size = 'medium',
  color = '#4f46e5',
  message
}) => {
  return (
    <div className={`inline-loader ${size}`}>
      <div className="inline-spinner" style={{ borderTopColor: color }}>
        <div className="spinner-inner"></div>
      </div>
      {message && <span className="inline-loader-message">{message}</span>}
    </div>
  );
};

export default InlineLoader;
