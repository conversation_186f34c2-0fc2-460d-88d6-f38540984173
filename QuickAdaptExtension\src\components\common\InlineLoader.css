/* Inline Loader */
.inline-loader {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.inline-spinner {
  border: 2px solid rgba(79, 70, 229, 0.2);
  border-top: 2px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: relative;
}

.inline-loader.small .inline-spinner {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

.inline-loader.medium .inline-spinner {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.inline-loader.large .inline-spinner {
  width: 24px;
  height: 24px;
  border-width: 3px;
}

.spinner-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background: currentColor;
  border-radius: 50%;
  opacity: 0.6;
  animation: pulse 1.5s ease-in-out infinite;
}

.inline-loader-message {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.inline-loader.small .inline-loader-message {
  font-size: 12px;
}

.inline-loader.large .inline-loader-message {
  font-size: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}
