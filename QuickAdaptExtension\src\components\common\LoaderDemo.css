.loader-demo {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.loader-demo h2 {
  color: #1a1a1a;
  margin-bottom: 32px;
  text-align: center;
}

.demo-section {
  margin-bottom: 40px;
  padding: 24px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: #f9fafb;
}

.demo-section h3 {
  color: #374151;
  margin-bottom: 20px;
  font-size: 18px;
}

.demo-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.demo-buttons button {
  min-width: 160px;
}

.inline-demo {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.inline-example {
  display: flex;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.usage-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.usage-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.usage-card h4 {
  color: #1f2937;
  margin-bottom: 8px;
  font-size: 16px;
}

.usage-card p {
  color: #6b7280;
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.5;
}

.usage-card code {
  display: block;
  background: #f3f4f6;
  padding: 8px 12px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #1f2937;
  word-break: break-all;
  white-space: pre-wrap;
}

@media (max-width: 768px) {
  .loader-demo {
    padding: 16px;
  }
  
  .demo-buttons {
    flex-direction: column;
  }
  
  .demo-buttons button {
    min-width: auto;
  }
  
  .usage-examples {
    grid-template-columns: 1fr;
  }
}
