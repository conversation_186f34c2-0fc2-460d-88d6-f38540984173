{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nexport const useExtensionInitialization = () => {\n  _s();\n  const [state, setState] = useState({\n    isInitializing: true,\n    isFirstLoad: true,\n    initializationComplete: false\n  });\n  useEffect(() => {\n    // Check if this is the first time the extension is being loaded\n    const hasBeenInitialized = sessionStorage.getItem('quickadapt-initialized');\n    if (!hasBeenInitialized) {\n      // First time loading - show popup loader\n      setState(prev => ({\n        ...prev,\n        isFirstLoad: true,\n        isInitializing: true\n      }));\n\n      // Simulate initialization process\n      const initializationSteps = [{\n        delay: 500,\n        message: 'Loading extension...'\n      }, {\n        delay: 1000,\n        message: 'Initializing components...'\n      }, {\n        delay: 1500,\n        message: 'Setting up environment...'\n      }, {\n        delay: 2000,\n        message: 'Almost ready...'\n      }, {\n        delay: 2500,\n        message: 'Complete!'\n      }];\n\n      // Complete initialization after all steps\n      setTimeout(() => {\n        setState(prev => ({\n          ...prev,\n          isInitializing: false,\n          initializationComplete: true\n        }));\n\n        // Mark as initialized in session storage\n        sessionStorage.setItem('quickadapt-initialized', 'true');\n      }, 3000);\n    } else {\n      // Already initialized in this session - skip popup\n      setState({\n        isInitializing: false,\n        isFirstLoad: false,\n        initializationComplete: true\n      });\n    }\n  }, []);\n  const resetInitialization = () => {\n    sessionStorage.removeItem('quickadapt-initialized');\n    setState({\n      isInitializing: true,\n      isFirstLoad: true,\n      initializationComplete: false\n    });\n  };\n  return {\n    ...state,\n    resetInitialization\n  };\n};\n_s(useExtensionInitialization, \"MitYUKnp/8mDPBoF/cMPaLNMGzw=\");", "map": {"version": 3, "names": ["useState", "useEffect", "useExtensionInitialization", "_s", "state", "setState", "isInitializing", "isFirstLoad", "initializationComplete", "hasBeenInitialized", "sessionStorage", "getItem", "prev", "initializationSteps", "delay", "message", "setTimeout", "setItem", "resetInitialization", "removeItem"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/hooks/useExtensionInitialization.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\n\ninterface ExtensionInitializationState {\n  isInitializing: boolean;\n  isFirstLoad: boolean;\n  initializationComplete: boolean;\n}\n\nexport const useExtensionInitialization = () => {\n  const [state, setState] = useState<ExtensionInitializationState>({\n    isInitializing: true,\n    isFirstLoad: true,\n    initializationComplete: false\n  });\n\n  useEffect(() => {\n    // Check if this is the first time the extension is being loaded\n    const hasBeenInitialized = sessionStorage.getItem('quickadapt-initialized');\n    \n    if (!hasBeenInitialized) {\n      // First time loading - show popup loader\n      setState(prev => ({\n        ...prev,\n        isFirstLoad: true,\n        isInitializing: true\n      }));\n\n      // Simulate initialization process\n      const initializationSteps = [\n        { delay: 500, message: 'Loading extension...' },\n        { delay: 1000, message: 'Initializing components...' },\n        { delay: 1500, message: 'Setting up environment...' },\n        { delay: 2000, message: 'Almost ready...' },\n        { delay: 2500, message: 'Complete!' }\n      ];\n\n      // Complete initialization after all steps\n      setTimeout(() => {\n        setState(prev => ({\n          ...prev,\n          isInitializing: false,\n          initializationComplete: true\n        }));\n        \n        // Mark as initialized in session storage\n        sessionStorage.setItem('quickadapt-initialized', 'true');\n      }, 3000);\n\n    } else {\n      // Already initialized in this session - skip popup\n      setState({\n        isInitializing: false,\n        isFirstLoad: false,\n        initializationComplete: true\n      });\n    }\n  }, []);\n\n  const resetInitialization = () => {\n    sessionStorage.removeItem('quickadapt-initialized');\n    setState({\n      isInitializing: true,\n      isFirstLoad: true,\n      initializationComplete: false\n    });\n  };\n\n  return {\n    ...state,\n    resetInitialization\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAQ3C,OAAO,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9C,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGL,QAAQ,CAA+B;IAC/DM,cAAc,EAAE,IAAI;IACpBC,WAAW,EAAE,IAAI;IACjBC,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EAEFP,SAAS,CAAC,MAAM;IACd;IACA,MAAMQ,kBAAkB,GAAGC,cAAc,CAACC,OAAO,CAAC,wBAAwB,CAAC;IAE3E,IAAI,CAACF,kBAAkB,EAAE;MACvB;MACAJ,QAAQ,CAACO,IAAI,KAAK;QAChB,GAAGA,IAAI;QACPL,WAAW,EAAE,IAAI;QACjBD,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMO,mBAAmB,GAAG,CAC1B;QAAEC,KAAK,EAAE,GAAG;QAAEC,OAAO,EAAE;MAAuB,CAAC,EAC/C;QAAED,KAAK,EAAE,IAAI;QAAEC,OAAO,EAAE;MAA6B,CAAC,EACtD;QAAED,KAAK,EAAE,IAAI;QAAEC,OAAO,EAAE;MAA4B,CAAC,EACrD;QAAED,KAAK,EAAE,IAAI;QAAEC,OAAO,EAAE;MAAkB,CAAC,EAC3C;QAAED,KAAK,EAAE,IAAI;QAAEC,OAAO,EAAE;MAAY,CAAC,CACtC;;MAED;MACAC,UAAU,CAAC,MAAM;QACfX,QAAQ,CAACO,IAAI,KAAK;UAChB,GAAGA,IAAI;UACPN,cAAc,EAAE,KAAK;UACrBE,sBAAsB,EAAE;QAC1B,CAAC,CAAC,CAAC;;QAEH;QACAE,cAAc,CAACO,OAAO,CAAC,wBAAwB,EAAE,MAAM,CAAC;MAC1D,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,MAAM;MACL;MACAZ,QAAQ,CAAC;QACPC,cAAc,EAAE,KAAK;QACrBC,WAAW,EAAE,KAAK;QAClBC,sBAAsB,EAAE;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,mBAAmB,GAAGA,CAAA,KAAM;IAChCR,cAAc,CAACS,UAAU,CAAC,wBAAwB,CAAC;IACnDd,QAAQ,CAAC;MACPC,cAAc,EAAE,IAAI;MACpBC,WAAW,EAAE,IAAI;MACjBC,sBAAsB,EAAE;IAC1B,CAAC,CAAC;EACJ,CAAC;EAED,OAAO;IACL,GAAGJ,KAAK;IACRc;EACF,CAAC;AACH,CAAC;AAACf,EAAA,CA/DWD,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}