import React from 'react';
import './ExtensionLoader.css';

interface ExtensionLoaderProps {
  message?: string;
  size?: 'small' | 'medium' | 'large';
  overlay?: boolean;
}

const ExtensionLoader: React.FC<ExtensionLoaderProps> = ({
  message = "Loading QuickAdapt Extension...",
  size = 'large',
  overlay = true
}) => {
  const containerClass = `extension-loader-container ${size}`;
  const spinnerClass = `extension-loader-spinner ${size}`;

  const content = (
    <div className={containerClass}>
      <div className="extension-loader-content">
        {/* Spinner */}
        <div className={spinnerClass}>
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
        </div>

        {/* Loading text */}
        <div className="extension-loader-text">
          <h3>{message}</h3>
          <div className="loading-dots">
            <span className="dot"></span>
            <span className="dot"></span>
            <span className="dot"></span>
          </div>
        </div>
      </div>
    </div>
  );

  if (overlay) {
    return (
      <div className="extension-loader-overlay">
        {content}
      </div>
    );
  }

  return content;
};

export default ExtensionLoader;
