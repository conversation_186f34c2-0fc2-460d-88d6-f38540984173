/* Extension Loader Overlay */
.extension-loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(5px);
}

/* Loader Container Box */
.extension-loader-container {
  background: #1a1a1a;
  border: 2px solid #333;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  text-align: center;
  min-width: 300px;
  max-width: 400px;
  position: relative;
  overflow: hidden;
}

.extension-loader-container.small {
  padding: 20px;
  min-width: 200px;
  max-width: 250px;
  border-radius: 12px;
}

.extension-loader-container.medium {
  padding: 30px;
  min-width: 250px;
  max-width: 320px;
  border-radius: 14px;
}

.extension-loader-container.large {
  padding: 40px;
  min-width: 300px;
  max-width: 400px;
  border-radius: 16px;
}

/* Add subtle glow effect */
.extension-loader-container::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #4f46e5, #7c3aed, #ec4899, #4f46e5);
  border-radius: 16px;
  z-index: -1;
  animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

/* Loader Content */
.extension-loader-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

/* Spinner Animation */
.extension-loader-spinner {
  position: relative;
  width: 80px;
  height: 80px;
}

.extension-loader-spinner.small {
  width: 40px;
  height: 40px;
}

.extension-loader-spinner.medium {
  width: 60px;
  height: 60px;
}

.extension-loader-spinner.large {
  width: 80px;
  height: 80px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: #4f46e5;
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  border-right-color: #7c3aed;
  animation-delay: 0.5s;
  width: 90%;
  height: 90%;
  top: 5%;
  left: 5%;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: #ec4899;
  animation-delay: 1s;
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
}

.spinner-ring:nth-child(4) {
  border-left-color: #06b6d4;
  animation-delay: 1.5s;
  width: 70%;
  height: 70%;
  top: 15%;
  left: 15%;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Loading Text */
.extension-loader-text {
  color: #ffffff;
}

.extension-loader-text h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 0.5px;
}

.extension-loader-container.small .extension-loader-text h3 {
  font-size: 14px;
  margin: 0 0 12px 0;
}

.extension-loader-container.medium .extension-loader-text h3 {
  font-size: 16px;
  margin: 0 0 14px 0;
}

.extension-loader-container.large .extension-loader-text h3 {
  font-size: 18px;
  margin: 0 0 16px 0;
}

/* Loading Dots Animation */
.loading-dots {
  display: flex;
  justify-content: center;
  gap: 4px;
}

.dot {
  width: 8px;
  height: 8px;
  background: #4f46e5;
  border-radius: 50%;
  animation: dotPulse 1.5s ease-in-out infinite;
}

.dot:nth-child(1) {
  animation-delay: 0s;
}

.dot:nth-child(2) {
  animation-delay: 0.3s;
  background: #7c3aed;
}

.dot:nth-child(3) {
  animation-delay: 0.6s;
  background: #ec4899;
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .extension-loader-container {
    margin: 20px;
    padding: 30px 20px;
    min-width: auto;
  }
  
  .extension-loader-spinner {
    width: 60px;
    height: 60px;
  }
  
  .extension-loader-text h3 {
    font-size: 16px;
  }
}
