{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\common\\\\ExtensionPopupLoader.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport './ExtensionPopupLoader.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExtensionPopupLoader = ({\n  message = \"QuickAdapt Extension Loading...\",\n  duration = 3000,\n  onComplete,\n  position = 'top-right'\n}) => {\n  _s();\n  const [isVisible, setIsVisible] = useState(true);\n  const [progress, setProgress] = useState(0);\n  useEffect(() => {\n    // Animate progress bar\n    const progressInterval = setInterval(() => {\n      setProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(progressInterval);\n          return 100;\n        }\n        return prev + 100 / (duration / 50); // Update every 50ms\n      });\n    }, 50);\n\n    // Auto hide after duration\n    const hideTimer = setTimeout(() => {\n      setIsVisible(false);\n      if (onComplete) {\n        setTimeout(onComplete, 300); // Wait for fade out animation\n      }\n    }, duration);\n    return () => {\n      clearInterval(progressInterval);\n      clearTimeout(hideTimer);\n    };\n  }, [duration, onComplete]);\n  if (!isVisible) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `extension-popup-loader ${position} ${isVisible ? 'visible' : 'hidden'}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"popup-loader-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"popup-loader-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"popup-loader-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"icon-spinner\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"popup-close-btn\",\n          onClick: () => setIsVisible(false),\n          \"aria-label\": \"Close\",\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"popup-loader-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"popup-loader-title\",\n          children: \"QuickAdapt\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"popup-loader-message\",\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"popup-progress-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"popup-progress-bar\",\n            style: {\n              width: `${progress}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"popup-loader-status\",\n          children: [progress < 30 && \"Initializing...\", progress >= 30 && progress < 70 && \"Loading components...\", progress >= 70 && progress < 100 && \"Almost ready...\", progress >= 100 && \"Ready!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"popup-loader-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"extension-badge\",\n          children: \"Extension\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(ExtensionPopupLoader, \"B3Heh1vmVMOVYZF5HYm7udhWEsk=\");\n_c = ExtensionPopupLoader;\nexport default ExtensionPopupLoader;\nvar _c;\n$RefreshReg$(_c, \"ExtensionPopupLoader\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "jsxDEV", "_jsxDEV", "ExtensionPopup<PERSON><PERSON>der", "message", "duration", "onComplete", "position", "_s", "isVisible", "setIsVisible", "progress", "setProgress", "progressInterval", "setInterval", "prev", "clearInterval", "hide<PERSON><PERSON>r", "setTimeout", "clearTimeout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "width", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/common/ExtensionPopupLoader.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport './ExtensionPopupLoader.css';\n\ninterface ExtensionPopupLoaderProps {\n  message?: string;\n  duration?: number;\n  onComplete?: () => void;\n  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';\n}\n\nconst ExtensionPopupLoader: React.FC<ExtensionPopupLoaderProps> = ({\n  message = \"QuickAdapt Extension Loading...\",\n  duration = 3000,\n  onComplete,\n  position = 'top-right'\n}) => {\n  const [isVisible, setIsVisible] = useState(true);\n  const [progress, setProgress] = useState(0);\n\n  useEffect(() => {\n    // Animate progress bar\n    const progressInterval = setInterval(() => {\n      setProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(progressInterval);\n          return 100;\n        }\n        return prev + (100 / (duration / 50)); // Update every 50ms\n      });\n    }, 50);\n\n    // Auto hide after duration\n    const hideTimer = setTimeout(() => {\n      setIsVisible(false);\n      if (onComplete) {\n        setTimeout(onComplete, 300); // Wait for fade out animation\n      }\n    }, duration);\n\n    return () => {\n      clearInterval(progressInterval);\n      clearTimeout(hideTimer);\n    };\n  }, [duration, onComplete]);\n\n  if (!isVisible) return null;\n\n  return (\n    <div className={`extension-popup-loader ${position} ${isVisible ? 'visible' : 'hidden'}`}>\n      <div className=\"popup-loader-container\">\n        {/* Header with icon and close button */}\n        <div className=\"popup-loader-header\">\n          <div className=\"popup-loader-icon\">\n            <div className=\"icon-spinner\">\n              <div className=\"spinner-dot\"></div>\n              <div className=\"spinner-dot\"></div>\n              <div className=\"spinner-dot\"></div>\n            </div>\n          </div>\n          <button \n            className=\"popup-close-btn\"\n            onClick={() => setIsVisible(false)}\n            aria-label=\"Close\"\n          >\n            ×\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"popup-loader-content\">\n          <div className=\"popup-loader-title\">QuickAdapt</div>\n          <div className=\"popup-loader-message\">{message}</div>\n          \n          {/* Progress bar */}\n          <div className=\"popup-progress-container\">\n            <div \n              className=\"popup-progress-bar\"\n              style={{ width: `${progress}%` }}\n            ></div>\n          </div>\n          \n          {/* Status text */}\n          <div className=\"popup-loader-status\">\n            {progress < 30 && \"Initializing...\"}\n            {progress >= 30 && progress < 70 && \"Loading components...\"}\n            {progress >= 70 && progress < 100 && \"Almost ready...\"}\n            {progress >= 100 && \"Ready!\"}\n          </div>\n        </div>\n\n        {/* Extension logo/branding area */}\n        <div className=\"popup-loader-footer\">\n          <div className=\"extension-badge\">Extension</div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ExtensionPopupLoader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASpC,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,OAAO,GAAG,iCAAiC;EAC3CC,QAAQ,GAAG,IAAI;EACfC,UAAU;EACVC,QAAQ,GAAG;AACb,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,CAAC,CAAC;EAE3CD,SAAS,CAAC,MAAM;IACd;IACA,MAAMc,gBAAgB,GAAGC,WAAW,CAAC,MAAM;MACzCF,WAAW,CAACG,IAAI,IAAI;QAClB,IAAIA,IAAI,IAAI,GAAG,EAAE;UACfC,aAAa,CAACH,gBAAgB,CAAC;UAC/B,OAAO,GAAG;QACZ;QACA,OAAOE,IAAI,GAAI,GAAG,IAAIV,QAAQ,GAAG,EAAE,CAAE,CAAC,CAAC;MACzC,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;;IAEN;IACA,MAAMY,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjCR,YAAY,CAAC,KAAK,CAAC;MACnB,IAAIJ,UAAU,EAAE;QACdY,UAAU,CAACZ,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC,EAAED,QAAQ,CAAC;IAEZ,OAAO,MAAM;MACXW,aAAa,CAACH,gBAAgB,CAAC;MAC/BM,YAAY,CAACF,SAAS,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,CAACZ,QAAQ,EAAEC,UAAU,CAAC,CAAC;EAE1B,IAAI,CAACG,SAAS,EAAE,OAAO,IAAI;EAE3B,oBACEP,OAAA;IAAKkB,SAAS,EAAE,0BAA0Bb,QAAQ,IAAIE,SAAS,GAAG,SAAS,GAAG,QAAQ,EAAG;IAAAY,QAAA,eACvFnB,OAAA;MAAKkB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErCnB,OAAA;QAAKkB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClCnB,OAAA;UAAKkB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCnB,OAAA;YAAKkB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BnB,OAAA;cAAKkB,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnCvB,OAAA;cAAKkB,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnCvB,OAAA;cAAKkB,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvB,OAAA;UACEkB,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAEA,CAAA,KAAMhB,YAAY,CAAC,KAAK,CAAE;UACnC,cAAW,OAAO;UAAAW,QAAA,EACnB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNvB,OAAA;QAAKkB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCnB,OAAA;UAAKkB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpDvB,OAAA;UAAKkB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAEjB;QAAO;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGrDvB,OAAA;UAAKkB,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvCnB,OAAA;YACEkB,SAAS,EAAC,oBAAoB;YAC9BO,KAAK,EAAE;cAAEC,KAAK,EAAE,GAAGjB,QAAQ;YAAI;UAAE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNvB,OAAA;UAAKkB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,GACjCV,QAAQ,GAAG,EAAE,IAAI,iBAAiB,EAClCA,QAAQ,IAAI,EAAE,IAAIA,QAAQ,GAAG,EAAE,IAAI,uBAAuB,EAC1DA,QAAQ,IAAI,EAAE,IAAIA,QAAQ,GAAG,GAAG,IAAI,iBAAiB,EACrDA,QAAQ,IAAI,GAAG,IAAI,QAAQ;QAAA;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvB,OAAA;QAAKkB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAClCnB,OAAA;UAAKkB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CAvFIL,oBAAyD;AAAA0B,EAAA,GAAzD1B,oBAAyD;AAyF/D,eAAeA,oBAAoB;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}